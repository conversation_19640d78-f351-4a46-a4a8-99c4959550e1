import numpy as np
import random
from scipy.io.wavfile import write

# ---------------------
# CONFIGURATION
# ---------------------
f_t = 7000  # Tinnitus frequency in Hz (example)
duration_per_tone = 0.2  # Duration of each tone in seconds
tones_per_burst = 4  # Number of tones in each burst
sampling_rate = 44100  # CD-quality audio
inter_burst_interval = 0.5  # silence between bursts
num_bursts = 30  # Number of bursts in total output

# Envelope function (5 ms linear ramp)
def envelope(signal, ramp_ms=5):
    ramp_samples = int((ramp_ms / 1000) * sampling_rate)
    ramp = np.linspace(0, 1, ramp_samples)
    env = np.ones_like(signal)
    env[:ramp_samples] = ramp

    env[-ramp_samples:] = ramp[::-1]
    return signal * env

# ---------------------
# FREQUENCIES
# ---------------------
# Generate 4 ACRN frequencies symmetrically around f_t
factors = [2**(-1/2), 2**(-1/4), 2**(1/4), 2**(1/2)]
frequencies = [f_t * f for f in factors]

# Generate 4 tones
tone_samples = int(duration_per_tone * sampling_rate)
tones = []
t = np.linspace(0, duration_per_tone, tone_samples, False)
for f in frequencies:
    tone = np.sin(2 * np.pi * f * t)
    tone = envelope(tone)
    tones.append(tone)

# ---------------------
# BURST PATTERN
# ---------------------
burst_sequence = []
inter_burst_samples = int(inter_burst_interval * sampling_rate)
silence = np.zeros(inter_burst_samples)

for _ in range(num_bursts):
    random.shuffle(tones)
    burst = np.concatenate(tones)
    burst_sequence.append(burst)
    burst_sequence.append(silence)

# Combine all bursts into final output
output = np.concatenate(burst_sequence)
output = output * 0.8  # volume scaling to avoid clipping
output = np.int16(output * 32767)  # convert to 16-bit PCM

# Save as WAV file
write("acrn_tones.wav", sampling_rate, output)
print("Saved ACRN tones to acrn_tones.wav")
